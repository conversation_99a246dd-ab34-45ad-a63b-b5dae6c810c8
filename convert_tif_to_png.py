#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TIF转PNG转换器
将textures文件夹中的所有TIF图片转换为PNG格式，并确保文件大小在1MB以下
"""

import os
import sys
from PIL import Image
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('conversion.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)

class TifToPngConverter:
    """TIF到PNG转换器类"""
    
    def __init__(self, textures_folder="textures", max_size_mb=1):
        """
        初始化转换器
        
        Args:
            textures_folder (str): 纹理文件夹路径
            max_size_mb (float): 最大文件大小（MB）
        """
        self.textures_folder = textures_folder
        self.max_size_bytes = max_size_mb * 1024 * 1024  # 转换为字节
        self.converted_count = 0
        self.failed_count = 0
        
    def get_file_size(self, filepath):
        """获取文件大小（字节）"""
        return os.path.getsize(filepath)
    
    def resize_image_to_target_size(self, image, target_path, quality=95):
        """
        调整图片大小以满足目标文件大小要求
        
        Args:
            image (PIL.Image): 原始图片对象
            target_path (str): 目标文件路径
            quality (int): 初始质量设置
            
        Returns:
            bool: 是否成功保存
        """
        original_size = image.size
        current_image = image.copy()
        
        # 首先尝试以原始尺寸保存
        current_image.save(target_path, "PNG", optimize=True)
        
        if self.get_file_size(target_path) <= self.max_size_bytes:
            logging.info(f"原始尺寸满足要求: {original_size}")
            return True
        
        # 如果文件过大，逐步缩小尺寸
        scale_factors = [0.9, 0.8, 0.7, 0.6, 0.5, 0.4, 0.3, 0.2]
        
        for scale in scale_factors:
            new_width = int(original_size[0] * scale)
            new_height = int(original_size[1] * scale)
            
            # 调整图片尺寸
            resized_image = current_image.resize((new_width, new_height), Image.Resampling.LANCZOS)
            
            # 保存并检查文件大小
            resized_image.save(target_path, "PNG", optimize=True)
            file_size = self.get_file_size(target_path)
            
            if file_size <= self.max_size_bytes:
                logging.info(f"缩放至 {scale:.1%} ({new_width}x{new_height}) 满足大小要求")
                return True
                
            logging.debug(f"缩放至 {scale:.1%} 后文件大小: {file_size/1024/1024:.2f}MB，仍然过大")
        
        logging.warning(f"无法将图片压缩到1MB以下，当前大小: {file_size/1024/1024:.2f}MB")
        return False
    
    def convert_single_file(self, tif_path, png_path):
        """
        转换单个TIF文件为PNG
        
        Args:
            tif_path (str): 源TIF文件路径
            png_path (str): 目标PNG文件路径
            
        Returns:
            bool: 转换是否成功
        """
        try:
            # 打开TIF图片
            with Image.open(tif_path) as img:
                # 如果是RGBA模式，保持透明度；否则转换为RGB
                if img.mode in ('RGBA', 'LA'):
                    converted_img = img.convert('RGBA')
                else:
                    converted_img = img.convert('RGB')
                
                # 调整大小并保存
                success = self.resize_image_to_target_size(converted_img, png_path)
                
                if success:
                    file_size_mb = self.get_file_size(png_path) / 1024 / 1024
                    logging.info(f"✓ 转换成功: {os.path.basename(tif_path)} -> {os.path.basename(png_path)} ({file_size_mb:.2f}MB)")
                    return True
                else:
                    logging.error(f"✗ 转换失败: {os.path.basename(tif_path)} - 无法压缩到1MB以下")
                    return False
                    
        except Exception as e:
            logging.error(f"✗ 转换失败: {os.path.basename(tif_path)} - 错误: {str(e)}")
            return False
    
    def convert_all_tif_files(self):
        """转换textures文件夹中的所有TIF文件"""
        
        # 检查textures文件夹是否存在
        if not os.path.exists(self.textures_folder):
            logging.error(f"错误: 找不到文件夹 '{self.textures_folder}'")
            return
        
        # 获取所有TIF文件
        tif_files = []
        for filename in os.listdir(self.textures_folder):
            if filename.lower().endswith(('.tif', '.tiff')):
                tif_files.append(filename)
        
        if not tif_files:
            logging.info(f"在 '{self.textures_folder}' 文件夹中没有找到TIF文件")
            return
        
        logging.info(f"找到 {len(tif_files)} 个TIF文件，开始转换...")
        
        # 转换每个文件
        for filename in tif_files:
            tif_path = os.path.join(self.textures_folder, filename)
            
            # 生成PNG文件名
            name_without_ext = os.path.splitext(filename)[0]
            png_filename = f"{name_without_ext}.png"
            png_path = os.path.join(self.textures_folder, png_filename)
            
            # 检查PNG文件是否已存在
            if os.path.exists(png_path):
                logging.warning(f"跳过: {png_filename} 已存在")
                continue
            
            # 执行转换
            if self.convert_single_file(tif_path, png_path):
                self.converted_count += 1
            else:
                self.failed_count += 1
        
        # 输出转换结果统计
        logging.info(f"\n转换完成!")
        logging.info(f"成功转换: {self.converted_count} 个文件")
        logging.info(f"转换失败: {self.failed_count} 个文件")
        logging.info(f"总计处理: {len(tif_files)} 个文件")

def main():
    """主函数"""
    print("TIF转PNG转换器")
    print("=" * 50)
    
    # 创建转换器实例
    converter = TifToPngConverter()
    
    # 执行转换
    converter.convert_all_tif_files()
    
    print("\n转换完成! 详细日志请查看 conversion.log 文件")

if __name__ == "__main__":
    main()
