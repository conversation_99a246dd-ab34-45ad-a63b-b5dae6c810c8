# TIF转PNG转换器

将textures文件夹中的所有TIF图片转换为PNG格式，并确保每张图片都保持在1MB以下。

## 安装依赖

```bash
pip install -r requirements.txt
```

## 使用方法

### 方法一：使用完整功能版本
```bash
python convert_tif_to_png.py
```

### 方法二：使用简化版本
```bash
python simple_tif_to_png.py
```

## 功能特性

- ✅ 自动扫描textures文件夹中的所有TIF文件
- ✅ 转换为PNG格式并保持图片质量
- ✅ 自动控制文件大小在1MB以下
- ✅ 智能缩放过大的图片
- ✅ 跳过已存在的PNG文件
- ✅ 支持透明度通道

## 文件说明

- `convert_tif_to_png.py` - 完整功能版本，包含详细日志和错误处理
- `simple_tif_to_png.py` - 简化版本，快速转换
- `requirements.txt` - 依赖包列表
- `conversion.log` - 转换日志文件（运行后生成）

## 注意事项

- 确保textures文件夹存在于脚本同级目录
- 转换过程中会自动跳过已存在的PNG文件
- 如果图片无法压缩到1MB以下，会在日志中显示警告
