#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Blender导出故障排除脚本
提供多种导出方案和问题解决方法
"""

import bpy
import os

def export_as_obj_with_materials():
    """导出为OBJ格式（更稳定）"""
    
    print("导出为OBJ格式...")
    
    # 获取当前文件路径
    blend_filepath = bpy.data.filepath
    if not blend_filepath:
        export_path = "C:/Users/<USER>/Downloads/Compressed/hugin_release_v2/exported_model.obj"
    else:
        export_path = os.path.splitext(blend_filepath)[0] + ".obj"
    
    try:
        # 导出OBJ
        bpy.ops.export_scene.obj(
            filepath=export_path,
            use_materials=True,
            use_uvs=True,
            use_normals=True,
            use_selection=False
        )
        print(f"✅ OBJ导出成功: {export_path}")
        return True
    except Exception as e:
        print(f"✗ OBJ导出失败: {str(e)}")
        return False

def export_as_fbx():
    """导出为FBX格式"""
    
    print("导出为FBX格式...")
    
    blend_filepath = bpy.data.filepath
    if not blend_filepath:
        export_path = "C:/Users/<USER>/Downloads/Compressed/hugin_release_v2/exported_model.fbx"
    else:
        export_path = os.path.splitext(blend_filepath)[0] + ".fbx"
    
    try:
        # 导出FBX
        bpy.ops.export_scene.fbx(
            filepath=export_path,
            use_selection=False,
            use_mesh_modifiers=True,
            use_custom_props=False,
            bake_space_transform=False
        )
        print(f"✅ FBX导出成功: {export_path}")
        return True
    except Exception as e:
        print(f"✗ FBX导出失败: {str(e)}")
        return False

def export_as_gltf():
    """导出为glTF格式"""
    
    print("导出为glTF格式...")
    
    blend_filepath = bpy.data.filepath
    if not blend_filepath:
        export_path = "C:/Users/<USER>/Downloads/Compressed/hugin_release_v2/exported_model.gltf"
    else:
        export_path = os.path.splitext(blend_filepath)[0] + ".gltf"
    
    try:
        # 导出glTF
        bpy.ops.export_scene.gltf(
            filepath=export_path,
            export_format='GLTF_SEPARATE',
            export_texcoords=True,
            export_normals=True,
            export_materials='EXPORT'
        )
        print(f"✅ glTF导出成功: {export_path}")
        return True
    except Exception as e:
        print(f"✗ glTF导出失败: {str(e)}")
        return False

def create_backup_materials():
    """创建简化的备份材质"""
    
    print("创建简化材质...")
    
    backup_materials = []
    
    for material in bpy.data.materials:
        if not material.use_nodes:
            continue
        
        # 创建简化版本
        backup_name = f"{material.name}_simple"
        if backup_name in bpy.data.materials:
            backup_mat = bpy.data.materials[backup_name]
        else:
            backup_mat = material.copy()
            backup_mat.name = backup_name
        
        # 简化节点树
        if backup_mat.use_nodes:
            nodes = backup_mat.node_tree.nodes
            links = backup_mat.node_tree.links
            
            # 清除所有节点
            nodes.clear()
            
            # 创建基本节点
            output_node = nodes.new(type='ShaderNodeOutputMaterial')
            bsdf_node = nodes.new(type='ShaderNodeBsdfPrincipled')
            
            # 连接节点
            links.new(bsdf_node.outputs['BSDF'], output_node.inputs['Surface'])
            
            # 设置基本颜色
            bsdf_node.inputs['Base Color'].default_value = (0.8, 0.8, 0.8, 1.0)
        
        backup_materials.append(backup_mat)
    
    print(f"创建了 {len(backup_materials)} 个简化材质")
    return backup_materials

def apply_simple_materials():
    """应用简化材质到所有对象"""
    
    print("应用简化材质...")
    
    # 创建一个通用的简单材质
    simple_mat_name = "Universal_Simple_Material"
    if simple_mat_name in bpy.data.materials:
        simple_mat = bpy.data.materials[simple_mat_name]
    else:
        simple_mat = bpy.data.materials.new(name=simple_mat_name)
        simple_mat.use_nodes = True
        
        nodes = simple_mat.node_tree.nodes
        links = simple_mat.node_tree.links
        
        # 清除默认节点
        nodes.clear()
        
        # 创建基本节点
        output_node = nodes.new(type='ShaderNodeOutputMaterial')
        bsdf_node = nodes.new(type='ShaderNodeBsdfPrincipled')
        
        # 连接节点
        links.new(bsdf_node.outputs['BSDF'], output_node.inputs['Surface'])
        
        # 设置材质属性
        bsdf_node.inputs['Base Color'].default_value = (0.7, 0.7, 0.7, 1.0)
        bsdf_node.inputs['Metallic'].default_value = 0.0
        bsdf_node.inputs['Roughness'].default_value = 0.5
    
    # 应用到所有网格对象
    applied_count = 0
    for obj in bpy.context.scene.objects:
        if obj.type == 'MESH':
            # 清除现有材质
            obj.data.materials.clear()
            # 添加简单材质
            obj.data.materials.append(simple_mat)
            applied_count += 1
    
    print(f"应用简单材质到 {applied_count} 个对象")

def main():
    """主函数 - 提供多种导出解决方案"""
    
    print("Blender导出故障排除工具")
    print("=" * 60)
    
    print("\n选择解决方案:")
    print("1. 尝试不同的导出格式")
    print("2. 简化材质后导出")
    print("3. 使用通用简单材质导出")
    
    # 方案1: 尝试不同格式
    print(f"\n=== 方案1: 尝试不同导出格式 ===")
    
    success_formats = []
    
    if export_as_obj_with_materials():
        success_formats.append("OBJ")
    
    if export_as_fbx():
        success_formats.append("FBX")
    
    if export_as_gltf():
        success_formats.append("glTF")
    
    if success_formats:
        print(f"\n✅ 成功导出格式: {', '.join(success_formats)}")
    else:
        print(f"\n⚠️ 所有格式导出都失败，尝试方案2...")
        
        # 方案2: 简化材质
        print(f"\n=== 方案2: 简化材质后导出 ===")
        create_backup_materials()
        
        # 再次尝试导出
        if export_as_obj_with_materials():
            print(f"✅ 简化材质后OBJ导出成功")
        else:
            print(f"⚠️ 仍然失败，尝试方案3...")
            
            # 方案3: 使用通用材质
            print(f"\n=== 方案3: 使用通用简单材质 ===")
            apply_simple_materials()
            
            if export_as_obj_with_materials():
                print(f"✅ 使用通用材质后导出成功")
            else:
                print(f"✗ 所有方案都失败，建议手动检查模型")

if __name__ == "__main__":
    main()
