#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复Blender导出材质问题的脚本
解决导出时的着色器和贴图相关错误
"""

import bpy

def fix_material_nodes():
    """修复材质节点问题"""
    
    print("开始修复材质节点问题...")
    print("=" * 50)
    
    fixed_count = 0
    removed_count = 0
    
    for material in bpy.data.materials:
        if not material.use_nodes:
            continue
            
        print(f"\n检查材质: {material.name}")
        
        # 获取节点树
        nodes = material.node_tree.nodes
        links = material.node_tree.links
        
        # 查找有问题的节点
        nodes_to_remove = []
        
        for node in nodes:
            if node.type == 'TEX_IMAGE':
                # 检查图像纹理节点
                if node.image is None:
                    print(f"  ✗ 移除空图像节点: {node.name}")
                    nodes_to_remove.append(node)
                elif not node.image.packed_file and node.image.filepath:
                    # 检查外部文件
                    import os
                    abs_path = bpy.path.abspath(node.image.filepath)
                    if not os.path.exists(abs_path):
                        print(f"  ✗ 移除无效图像节点: {node.name} (文件不存在)")
                        nodes_to_remove.append(node)
                    else:
                        print(f"  ✓ 图像节点正常: {node.name}")
                else:
                    print(f"  ✓ 图像节点正常: {node.name}")
        
        # 移除有问题的节点
        for node in nodes_to_remove:
            nodes.remove(node)
            removed_count += 1
        
        # 确保材质有基本的输出节点
        output_node = None
        for node in nodes:
            if node.type == 'OUTPUT_MATERIAL':
                output_node = node
                break
        
        if not output_node:
            output_node = nodes.new(type='ShaderNodeOutputMaterial')
            print(f"  + 添加输出节点")
        
        # 确保有基本的BSDF节点
        bsdf_node = None
        for node in nodes:
            if node.type == 'BSDF_PRINCIPLED':
                bsdf_node = node
                break
        
        if not bsdf_node:
            bsdf_node = nodes.new(type='ShaderNodeBsdfPrincipled')
            print(f"  + 添加BSDF节点")
        
        # 连接基本节点
        if not output_node.inputs['Surface'].is_linked:
            links.new(bsdf_node.outputs['BSDF'], output_node.inputs['Surface'])
            print(f"  + 连接BSDF到输出")
        
        fixed_count += 1
    
    print(f"\n修复完成:")
    print(f"处理的材质: {fixed_count}")
    print(f"移除的问题节点: {removed_count}")

def simplify_materials_for_export():
    """为导出简化材质"""
    
    print("\n简化材质以便导出...")
    print("=" * 30)
    
    for material in bpy.data.materials:
        if not material.use_nodes:
            continue
            
        nodes = material.node_tree.nodes
        
        # 查找复杂的节点类型并简化
        complex_nodes = []
        for node in nodes:
            if node.type in ['TEX_NOISE', 'TEX_VORONOI', 'TEX_MUSGRAVE', 'MATH', 'VECT_MATH']:
                complex_nodes.append(node)
        
        if complex_nodes:
            print(f"材质 {material.name} 包含 {len(complex_nodes)} 个复杂节点")
            # 可以选择移除或保留，这里我们保留但记录
        
        # 确保所有图像节点都有有效的图像
        for node in nodes:
            if node.type == 'TEX_IMAGE' and node.image:
                if not node.image.packed_file and not node.image.filepath:
                    print(f"  警告: {material.name} 中的 {node.name} 没有有效图像")

def check_export_readiness():
    """检查导出准备情况"""
    
    print("\n检查导出准备情况...")
    print("=" * 30)
    
    # 检查场景对象
    mesh_objects = [obj for obj in bpy.context.scene.objects if obj.type == 'MESH']
    print(f"网格对象数量: {len(mesh_objects)}")
    
    # 检查材质
    materials_with_textures = 0
    for material in bpy.data.materials:
        if material.use_nodes:
            has_textures = any(node.type == 'TEX_IMAGE' and node.image 
                             for node in material.node_tree.nodes)
            if has_textures:
                materials_with_textures += 1
    
    print(f"有贴图的材质: {materials_with_textures}")
    
    # 检查打包的图像
    packed_images = sum(1 for img in bpy.data.images if img.packed_file)
    print(f"打包的图像: {packed_images}")
    
    print(f"\n✅ 导出准备检查完成")

def main():
    """主函数"""
    print("Blender导出材质修复工具")
    print("=" * 60)
    
    # 执行修复步骤
    fix_material_nodes()
    simplify_materials_for_export()
    check_export_readiness()
    
    print(f"\n🎉 修复完成!")
    print(f"建议:")
    print(f"1. 保存Blender文件")
    print(f"2. 尝试重新导出")
    print(f"3. 如果仍有问题，考虑导出为更简单的格式（如OBJ）")

if __name__ == "__main__":
    main()
