#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版TIF转PNG转换器
快速将textures文件夹中的TIF图片转换为PNG，确保文件大小在1MB以下
"""

import os
from PIL import Image

def convert_tif_to_png(textures_folder="textures", max_size_mb=1):
    """
    转换TIF文件为PNG格式，并控制文件大小
    
    Args:
        textures_folder (str): 纹理文件夹路径
        max_size_mb (float): 最大文件大小（MB）
    """
    max_size_bytes = max_size_mb * 1024 * 1024
    
    if not os.path.exists(textures_folder):
        print(f"错误: 找不到文件夹 '{textures_folder}'")
        return
    
    # 获取所有TIF文件
    tif_files = [f for f in os.listdir(textures_folder) 
                 if f.lower().endswith(('.tif', '.tiff'))]
    
    if not tif_files:
        print(f"在 '{textures_folder}' 文件夹中没有找到TIF文件")
        return
    
    print(f"找到 {len(tif_files)} 个TIF文件")
    converted = 0
    
    for filename in tif_files:
        tif_path = os.path.join(textures_folder, filename)
        png_filename = os.path.splitext(filename)[0] + ".png"
        png_path = os.path.join(textures_folder, png_filename)
        
        # 跳过已存在的PNG文件
        if os.path.exists(png_path):
            print(f"跳过: {png_filename} 已存在")
            continue
        
        try:
            with Image.open(tif_path) as img:
                # 转换颜色模式
                if img.mode in ('RGBA', 'LA'):
                    converted_img = img.convert('RGBA')
                else:
                    converted_img = img.convert('RGB')
                
                # 先尝试原始尺寸保存
                converted_img.save(png_path, "PNG", optimize=True)
                
                # 检查文件大小，如果过大则缩小
                while os.path.getsize(png_path) > max_size_bytes:
                    # 缩小20%
                    width, height = converted_img.size
                    new_width = int(width * 0.8)
                    new_height = int(height * 0.8)
                    
                    if new_width < 50 or new_height < 50:  # 防止图片过小
                        print(f"警告: {filename} 无法压缩到1MB以下")
                        break
                    
                    converted_img = converted_img.resize((new_width, new_height), Image.Resampling.LANCZOS)
                    converted_img.save(png_path, "PNG", optimize=True)
                
                file_size_mb = os.path.getsize(png_path) / 1024 / 1024
                print(f"✓ {filename} -> {png_filename} ({file_size_mb:.2f}MB)")
                converted += 1
                
        except Exception as e:
            print(f"✗ 转换失败: {filename} - {str(e)}")
    
    print(f"\n转换完成! 成功转换 {converted} 个文件")

if __name__ == "__main__":
    convert_tif_to_png()
